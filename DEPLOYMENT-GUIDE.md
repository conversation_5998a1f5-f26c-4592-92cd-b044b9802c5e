# DCF Logistics - GoDaddy Shared Hosting Deployment Guide

## 📋 Overview
This guide explains how to deploy the DCF Logistics static website to GoDaddy shared hosting using cPanel File Manager.

## 🎯 What's Included
- ✅ **Static HTML/CSS/JS files** - No server-side dependencies
- ✅ **Responsive design** - Works on all devices
- ✅ **Contact forms** - Uses mailto functionality
- ✅ **Quote requests** - Email-based submission
- ✅ **Service pages** - Complete logistics services showcase
- ✅ **SEO optimized** - Meta tags and structured content
- ✅ **Apache configuration** - .htaccess file included

## 🚀 Deployment Steps

### Step 1: Access cPanel
1. Log into your GoDaddy hosting account
2. Navigate to **cPanel**
3. Find and click **File Manager**

### Step 2: Prepare the Directory
1. Navigate to `public_html` (or your domain's root directory)
2. **IMPORTANT**: Backup any existing files
3. Delete old website files (if replacing an existing site)

### Step 3: Upload Files
1. In File Manager, click **Upload**
2. Select all files from the `/out` directory:
   - All `.html` files
   - `_next/` folder (contains CSS, JS, and assets)
   - `.htaccess` file
   - Any image/asset folders

### Step 4: Extract and Organize
1. If you uploaded a ZIP file, extract it in the `public_html` directory
2. Ensure the file structure looks like this:
   ```
   public_html/
   ├── index.html
   ├── about.html
   ├── contact.html
   ├── services.html
   ├── .htaccess
   ├── _next/
   │   ├── static/
   │   └── ...
   └── ...
   ```

### Step 5: Set Permissions
1. Select the `.htaccess` file
2. Right-click → **Permissions**
3. Set to **644** (rw-r--r--)
4. For directories, ensure they're set to **755** (rwxr-xr-x)

### Step 6: Test the Website
1. Visit your domain in a web browser
2. Test all major pages:
   - Homepage (/)
   - About (/about)
   - Services (/services)
   - Contact (/contact)
   - Quote (/quote)
   - Tracking (/tracking)

## 📧 Contact Form Configuration

The contact forms use **mailto** functionality:
- **General inquiries**: <EMAIL>
- **Quote requests**: <EMAIL>
- **Support**: <EMAIL>

### To customize email addresses:
1. Edit the static data in the deployed files
2. Or rebuild with updated email addresses in `lib/static-data.ts`

## 🔧 Customization Options

### Update Company Information
Edit these files to update company details:
- Contact information in contact pages
- Service descriptions in service pages
- About page content

### Add/Remove Services
The services are defined in the static data. To modify:
1. Update the source code in `lib/static-data.ts`
2. Rebuild the static site
3. Redeploy the updated files

## 🌐 Domain Configuration

### Custom Domain Setup
1. In GoDaddy DNS management, ensure your domain points to your hosting
2. If using a subdomain, create appropriate DNS records
3. Update any hardcoded URLs in the site if necessary

### SSL Certificate
1. Enable SSL in your GoDaddy hosting panel
2. Uncomment the HTTPS redirect lines in `.htaccess`:
   ```apache
   RewriteCond %{HTTPS} off
   RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
   ```

## 📊 Performance Optimization

The static site includes:
- ✅ **Compressed assets** - Gzip compression enabled
- ✅ **Browser caching** - 1-year cache for static assets
- ✅ **Optimized images** - WebP format where supported
- ✅ **Minified CSS/JS** - Reduced file sizes
- ✅ **CDN-ready** - Can be used with CloudFlare or similar

## 🔍 SEO Features

- ✅ **Meta tags** - Title, description, keywords
- ✅ **Open Graph** - Social media sharing
- ✅ **Structured data** - Schema.org markup
- ✅ **Sitemap** - XML sitemap included
- ✅ **Robots.txt** - Search engine directives

## 🛠️ Troubleshooting

### Common Issues:

**1. 404 Errors on Page Refresh**
- Ensure `.htaccess` file is uploaded and has correct permissions
- Check that mod_rewrite is enabled on your hosting

**2. CSS/JS Not Loading**
- Verify the `_next/` folder was uploaded completely
- Check file permissions (should be 644 for files, 755 for directories)

**3. Contact Forms Not Working**
- Ensure your email client is configured
- Check that mailto links are not blocked by browser

**4. Images Not Displaying**
- Verify all image files were uploaded
- Check image file paths and permissions

### Support
For technical support with the website:
- Check the deployment files and structure
- Verify all static assets are properly uploaded
- Contact your hosting provider for server-specific issues

## 📁 File Structure Reference

```
public_html/
├── index.html                 # Homepage
├── about/index.html          # About page
├── contact/index.html        # Contact page
├── services/index.html       # Services overview
├── services/air-freight/index.html
├── services/customs-clearance/index.html
├── quote/index.html          # Quote request
├── tracking/index.html       # Shipment tracking
├── support/index.html        # Support page
├── .htaccess                 # Apache configuration
├── _next/                    # Next.js assets
│   ├── static/css/           # Stylesheets
│   ├── static/js/            # JavaScript files
│   └── static/media/         # Images and fonts
├── 404.html                  # Error page
└── sitemap.xml              # SEO sitemap
```

## ✅ Deployment Checklist

- [ ] Backup existing website files
- [ ] Upload all files from `/out` directory
- [ ] Set correct file permissions
- [ ] Test all major pages
- [ ] Verify contact forms work
- [ ] Check mobile responsiveness
- [ ] Test page loading speed
- [ ] Verify SSL certificate (if applicable)
- [ ] Submit sitemap to search engines

## 🎉 Success!

Your DCF Logistics website is now live on GoDaddy shared hosting! The static site provides:
- Fast loading times
- Mobile-responsive design
- Professional logistics showcase
- Working contact and quote forms
- SEO optimization
- Secure hosting configuration
