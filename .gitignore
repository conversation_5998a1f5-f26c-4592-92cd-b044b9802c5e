# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# next.js
/.next/
/out/
/dist/
/dist-package/

# production
/build

# env files
.env*
.env.local
.env.production
.env.development.local
.env.test.local
.env.production.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# database
*.db
*.sqlite
*.sqlite3
database.sqlite
prisma/dev.db*

# deployment packages
*.zip
dcf-logistics-*.zip
dcf-logistics-cpanel-deployment.zip
dcf-logistics-portable-*.zip

# deployment configs (contain sensitive data)
deployment/godaddy/config.json
deployment/cpanel/config.json

# backups
backups/
*.backup
*.bak

# logs
logs
*.log

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# cache
.cache/
.parcel-cache/
.turbo

# testing
coverage/
.nyc_output/

# misc
*.tgz
*.tar.gz
.vercel
