'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'

// Mock user type for static export
interface StaticUser {
  id: string
  name: string
  email: string
  role: 'admin' | 'customer' | 'user'
  image?: string
}

interface StaticAuthContextType {
  user: StaticUser | null
  isLoading: boolean
  signIn: (email: string, password: string) => Promise<boolean>
  signOut: () => void
  isAuthenticated: boolean
}

const StaticAuthContext = createContext<StaticAuthContextType | undefined>(undefined)

// Demo users for static export
const demoUsers: StaticUser[] = [
  {
    id: '1',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin',
    image: '/images/avatars/admin.jpg',
  },
  {
    id: '2',
    name: 'Customer Demo',
    email: '<EMAIL>',
    role: 'customer',
    image: '/images/avatars/customer.jpg',
  },
]

export function StaticAuth<PERSON>rovider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<StaticUser | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Check for stored user in localStorage
    const storedUser = localStorage.getItem('static-auth-user')
    if (storedUser) {
      try {
        setUser(JSON.parse(storedUser))
      } catch (error) {
        localStorage.removeItem('static-auth-user')
      }
    }
    setIsLoading(false)
  }, [])

  const signIn = async (email: string, password: string): Promise<boolean> => {
    setIsLoading(true)
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Find demo user
    const foundUser = demoUsers.find(u => u.email === email)
    
    if (foundUser && (password === 'demo123' || password === 'admin123')) {
      setUser(foundUser)
      localStorage.setItem('static-auth-user', JSON.stringify(foundUser))
      setIsLoading(false)
      return true
    }
    
    setIsLoading(false)
    return false
  }

  const signOut = () => {
    setUser(null)
    localStorage.removeItem('static-auth-user')
  }

  const value: StaticAuthContextType = {
    user,
    isLoading,
    signIn,
    signOut,
    isAuthenticated: !!user,
  }

  return (
    <StaticAuthContext.Provider value={value}>
      {children}
    </StaticAuthContext.Provider>
  )
}

export function useStaticAuth() {
  const context = useContext(StaticAuthContext)
  if (context === undefined) {
    throw new Error('useStaticAuth must be used within a StaticAuthProvider')
  }
  return context
}

// Mock session hook to replace useSession from next-auth
export function useSession() {
  const { user, isLoading } = useStaticAuth()
  
  return {
    data: user ? { user } : null,
    status: isLoading ? 'loading' : user ? 'authenticated' : 'unauthenticated',
  }
}

// Mock signIn function to replace next-auth signIn
export async function signIn(provider?: string, options?: any) {
  if (typeof window !== 'undefined') {
    // For static export, redirect to a demo login page or show alert
    alert('This is a demo version. Use email: <EMAIL>, password: demo123')
    return { error: null, status: 200, ok: true, url: null }
  }
}

// Mock signOut function to replace next-auth signOut
export async function signOut(options?: any) {
  if (typeof window !== 'undefined') {
    const { signOut: staticSignOut } = useStaticAuth()
    staticSignOut()
    if (options?.callbackUrl) {
      window.location.href = options.callbackUrl
    }
  }
}

// Mock getServerSession for static export
export async function getServerSession() {
  return null // Always return null for static export
}
