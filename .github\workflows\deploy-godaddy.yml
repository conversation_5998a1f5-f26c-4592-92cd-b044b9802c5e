name: Deploy to GoDaddy

on:
  push:
    branches: [ main, production ]
  workflow_dispatch:
    inputs:
      environment:
        description: 'Deployment environment'
        required: true
        default: 'production'
        type: choice
        options:
        - production
        - staging

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Setup environment variables
      run: |
        echo "NEXTAUTH_URL=${{ secrets.NEXTAUTH_URL }}" >> .env.production
        echo "NEXTAUTH_SECRET=${{ secrets.NEXTAUTH_SECRET }}" >> .env.production
        echo "DATABASE_URL=${{ secrets.DATABASE_URL }}" >> .env.production
        echo "EMAIL_FROM=${{ secrets.EMAIL_FROM }}" >> .env.production
        echo "SENDGRID_API_KEY=${{ secrets.SENDGRID_API_KEY }}" >> .env.production
        echo "STRIPE_PUBLIC_KEY=${{ secrets.STRIPE_PUBLIC_KEY }}" >> .env.production
        echo "STRIPE_SECRET_KEY=${{ secrets.STRIPE_SECRET_KEY }}" >> .env.production
        echo "CLOUDINARY_CLOUD_NAME=${{ secrets.CLOUDINARY_CLOUD_NAME }}" >> .env.production
        echo "CLOUDINARY_API_KEY=${{ secrets.CLOUDINARY_API_KEY }}" >> .env.production
        echo "CLOUDINARY_API_SECRET=${{ secrets.CLOUDINARY_API_SECRET }}" >> .env.production
        
    - name: Generate Prisma client
      run: npx prisma generate
      
    - name: Run tests
      run: npm run test:ci || true
      
    - name: Build application
      run: |
        cp deployment/godaddy/next.config.production.mjs next.config.mjs
        npm run build:godaddy
        
    - name: Create deployment config
      run: |
        cat > deployment/godaddy/config.json << EOF
        {
          "ftp": {
            "host": "${{ secrets.FTP_HOST }}",
            "user": "${{ secrets.FTP_USER }}",
            "password": "${{ secrets.FTP_PASSWORD }}",
            "port": 21,
            "secure": false
          },
          "remoteDir": "${{ secrets.FTP_REMOTE_DIR }}",
          "siteUrl": "${{ secrets.SITE_URL }}",
          "environment": {
            "NEXTAUTH_URL": "${{ secrets.NEXTAUTH_URL }}",
            "DATABASE_URL": "${{ secrets.DATABASE_URL }}"
          }
        }
        EOF
        
    - name: Deploy to GoDaddy
      run: node deployment/godaddy/scripts/deploy-godaddy.js
      
    - name: Run post-deployment tests
      run: |
        sleep 30
        curl -f ${{ secrets.SITE_URL }} || exit 1
        curl -f ${{ secrets.SITE_URL }}/api/health || exit 1
        
    - name: Notify deployment status
      if: always()
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        fields: repo,message,commit,author,action,eventName,ref,workflow
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK }}
        
    - name: Create deployment artifact
      if: success()
      uses: actions/upload-artifact@v4
      with:
        name: deployment-${{ github.sha }}
        path: |
          out/
          deployment/godaddy/config.json
        retention-days: 30
        
  rollback:
    runs-on: ubuntu-latest
    needs: deploy
    if: failure()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        
    - name: Install dependencies
      run: npm ci
      
    - name: Rollback deployment
      run: |
        echo "Deployment failed, initiating rollback..."
        node deployment/godaddy/scripts/rollback.js
        
    - name: Notify rollback
      uses: 8398a7/action-slack@v3
      with:
        status: 'warning'
        channel: '#deployments'
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
        text: '🔄 Deployment failed, rollback initiated for ${{ github.repository }}'
