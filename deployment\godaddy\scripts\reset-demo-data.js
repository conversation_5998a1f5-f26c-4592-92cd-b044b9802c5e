#!/usr/bin/env node

/**
 * Demo Data Reset Script for DCF Logistics
 * Resets demo data to clean state for client presentations
 */

const fs = require('fs')
const path = require('path')

class DemoDataReset {
  constructor() {
    this.projectRoot = process.cwd()
    this.deploymentDir = path.join(this.projectRoot, 'deployment', 'godaddy')
    this.config = this.loadDemoConfig()
  }

  loadDemoConfig() {
    const configPath = path.join(this.deploymentDir, 'config.demo.json')
    if (!fs.existsSync(configPath)) {
      throw new Error(`Demo config not found: ${configPath}`)
    }
    return JSON.parse(fs.readFileSync(configPath, 'utf8'))
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString()
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️'
    console.log(`${prefix} [${timestamp}] ${message}`)
  }

  generateResetScript() {
    this.log('Generating demo data reset script...')
    
    const resetScript = `-- DCF Logistics Demo Data Reset Script
-- This script resets the demo database to a clean state
-- Generated on: ${new Date().toISOString()}

USE ${this.config.database.database};

-- Disable foreign key checks temporarily
SET FOREIGN_KEY_CHECKS = 0;

-- Clear existing demo data (keep structure)
DELETE FROM shipment_updates WHERE shipmentId LIKE 'demo-%';
DELETE FROM invoice_items WHERE invoiceId LIKE 'demo-%';
DELETE FROM quote_items WHERE quoteId LIKE 'demo-%';
DELETE FROM shipments WHERE id LIKE 'demo-%';
DELETE FROM invoices WHERE id LIKE 'demo-%';
DELETE FROM quotes WHERE id LIKE 'demo-%';
DELETE FROM inquiries WHERE id LIKE 'demo-%';
DELETE FROM customers WHERE id LIKE 'demo-%';
DELETE FROM users WHERE id LIKE 'demo-%';

-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;

-- Insert fresh demo data
-- Demo Admin User
INSERT INTO users (id, email, name, role, password, emailVerified, createdAt, updatedAt) VALUES
('demo-admin-001', '<EMAIL>', 'Demo Administrator', 'ADMIN', '$2a$12$LQv3c1yqBwuvHiOjgGiMyOeBSR6wqls2ac6P5kVkE6IOQQ1QpHiG2', NOW(), NOW(), NOW());

-- Demo Customers
INSERT INTO customers (id, name, email, phone, company, address, city, country, postalCode, createdAt, updatedAt) VALUES
('demo-customer-001', 'John Demo Smith', '<EMAIL>', '+************', 'Demo Trading Ltd', '123 Demo Street', 'Banjul', 'Gambia', '1001', NOW(), NOW()),
('demo-customer-002', 'Sarah Demo Johnson', '<EMAIL>', '+************', 'Demo Corporation', '456 Business Ave', 'Serrekunda', 'Gambia', '1002', NOW(), NOW()),
('demo-customer-003', 'Mike Demo Wilson', '<EMAIL>', '+************', 'Demo Enterprises', '789 Commerce Blvd', 'Bakau', 'Gambia', '1003', NOW(), NOW());

-- Demo Inquiries
INSERT INTO inquiries (id, name, email, phone, company, subject, message, type, status, createdAt, updatedAt) VALUES
('demo-inquiry-001', 'Alice Demo Brown', '<EMAIL>', '+************', 'New Client Co', 'Shipping Quote Request', 'I need a quote for shipping 50 boxes from Gambia to UK', 'QUOTE', 'PENDING', NOW(), NOW()),
('demo-inquiry-002', 'Bob Demo Davis', '<EMAIL>', '+************', 'Fast Ship Inc', 'Express Delivery Service', 'Looking for express delivery options for urgent shipments', 'GENERAL', 'RESPONDED', DATE_SUB(NOW(), INTERVAL 1 DAY), NOW());

-- Demo Quotes
INSERT INTO quotes (id, customerId, quoteNumber, serviceType, origin, destination, weight, dimensions, quotedAmount, currency, validUntil, status, notes, createdAt, updatedAt) VALUES
('demo-quote-001', 'demo-customer-001', 'QT-DEMO-001', 'AIR_FREIGHT', 'Banjul, Gambia', 'London, UK', 45.5, '60x40x35 cm', 850.00, 'USD', DATE_ADD(NOW(), INTERVAL 30 DAY), 'PENDING', 'Standard air freight service', NOW(), NOW()),
('demo-quote-002', 'demo-customer-002', 'QT-DEMO-002', 'SEA_FREIGHT', 'Banjul, Gambia', 'Hamburg, Germany', 1250.0, '120x80x100 cm', 1200.00, 'USD', DATE_ADD(NOW(), INTERVAL 30 DAY), 'APPROVED', 'Sea freight with insurance', DATE_SUB(NOW(), INTERVAL 2 DAY), NOW()),
('demo-quote-003', 'demo-customer-003', 'QT-DEMO-003', 'EXPRESS', 'Banjul, Gambia', 'New York, USA', 12.5, '30x25x20 cm', 450.00, 'USD', DATE_ADD(NOW(), INTERVAL 15 DAY), 'SENT', 'Express delivery service', DATE_SUB(NOW(), INTERVAL 1 DAY), NOW());

-- Demo Invoices
INSERT INTO invoices (id, customerId, invoiceNumber, quoteId, totalAmount, currency, status, issueDate, dueDate, paidDate, notes, createdAt, updatedAt) VALUES
('demo-invoice-001', 'demo-customer-001', 'INV-DEMO-001', 'demo-quote-001', 850.00, 'USD', 'PENDING', NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), NULL, 'Air freight service payment', NOW(), NOW()),
('demo-invoice-002', 'demo-customer-002', 'INV-DEMO-002', 'demo-quote-002', 1200.00, 'USD', 'PAID', DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_ADD(NOW(), INTERVAL 25 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), 'Sea freight service - paid', DATE_SUB(NOW(), INTERVAL 5 DAY), NOW());

-- Demo Shipments
INSERT INTO shipments (id, customerId, invoiceId, trackingNumber, serviceType, origin, destination, weight, dimensions, status, estimatedDelivery, actualDelivery, notes, createdAt, updatedAt) VALUES
('demo-shipment-001', 'demo-customer-002', 'demo-invoice-002', 'DCF-DEMO-001', 'SEA_FREIGHT', 'Banjul, Gambia', 'Hamburg, Germany', 1250.0, '120x80x100 cm', 'IN_TRANSIT', DATE_ADD(NOW(), INTERVAL 14 DAY), NULL, 'Sea freight shipment in transit', DATE_SUB(NOW(), INTERVAL 3 DAY), NOW()),
('demo-shipment-002', 'demo-customer-001', NULL, 'DCF-DEMO-002', 'AIR_FREIGHT', 'London, UK', 'Banjul, Gambia', 25.5, '50x40x30 cm', 'DELIVERED', DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY), 'Delivered successfully', DATE_SUB(NOW(), INTERVAL 7 DAY), NOW()),
('demo-shipment-003', 'demo-customer-003', NULL, 'DCF-DEMO-003', 'EXPRESS', 'Banjul, Gambia', 'New York, USA', 12.5, '30x25x20 cm', 'PROCESSING', DATE_ADD(NOW(), INTERVAL 3 DAY), NULL, 'Express shipment being processed', NOW(), NOW());

-- Demo Shipment Updates
INSERT INTO shipment_updates (id, shipmentId, status, location, description, timestamp, createdAt) VALUES
('demo-update-001', 'demo-shipment-001', 'PICKED_UP', 'Banjul Port, Gambia', 'Package picked up from sender', DATE_SUB(NOW(), INTERVAL 3 DAY), DATE_SUB(NOW(), INTERVAL 3 DAY)),
('demo-update-002', 'demo-shipment-001', 'IN_TRANSIT', 'Atlantic Ocean', 'Shipment loaded on vessel MV Demo Carrier', DATE_SUB(NOW(), INTERVAL 2 DAY), DATE_SUB(NOW(), INTERVAL 2 DAY)),
('demo-update-003', 'demo-shipment-002', 'DELIVERED', 'Banjul, Gambia', 'Package delivered to recipient', DATE_SUB(NOW(), INTERVAL 1 DAY), DATE_SUB(NOW(), INTERVAL 1 DAY)),
('demo-update-004', 'demo-shipment-003', 'PROCESSING', 'DCF Logistics Warehouse', 'Package received and being processed', NOW(), NOW());

-- Demo Quote Items
INSERT INTO quote_items (id, quoteId, description, quantity, unitPrice, totalPrice, createdAt) VALUES
('demo-qitem-001', 'demo-quote-001', 'Air freight service (45.5 kg)', 1, 850.00, 850.00, NOW()),
('demo-qitem-002', 'demo-quote-002', 'Sea freight service (1250 kg)', 1, 1000.00, 1000.00, DATE_SUB(NOW(), INTERVAL 2 DAY)),
('demo-qitem-003', 'demo-quote-002', 'Insurance coverage', 1, 200.00, 200.00, DATE_SUB(NOW(), INTERVAL 2 DAY)),
('demo-qitem-004', 'demo-quote-003', 'Express delivery service (12.5 kg)', 1, 450.00, 450.00, DATE_SUB(NOW(), INTERVAL 1 DAY));

-- Demo Invoice Items
INSERT INTO invoice_items (id, invoiceId, description, quantity, unitPrice, totalPrice, createdAt) VALUES
('demo-iitem-001', 'demo-invoice-001', 'Air freight service (45.5 kg)', 1, 850.00, 850.00, NOW()),
('demo-iitem-002', 'demo-invoice-002', 'Sea freight service (1250 kg)', 1, 1000.00, 1000.00, DATE_SUB(NOW(), INTERVAL 5 DAY)),
('demo-iitem-003', 'demo-invoice-002', 'Insurance coverage', 1, 200.00, 200.00, DATE_SUB(NOW(), INTERVAL 5 DAY));

-- Update statistics
UPDATE system_settings SET value = '3' WHERE key = 'total_customers';
UPDATE system_settings SET value = '3' WHERE key = 'total_shipments';
UPDATE system_settings SET value = '3' WHERE key = 'total_quotes';
UPDATE system_settings SET value = '2' WHERE key = 'total_invoices';

-- Demo data reset completed
SELECT 'Demo data reset completed successfully!' as status;
`

    return resetScript
  }

  async createResetScript() {
    this.log('Creating demo data reset script...')
    
    const resetScript = this.generateResetScript()
    const scriptPath = path.join(this.projectRoot, 'demo-data-reset.sql')
    
    fs.writeFileSync(scriptPath, resetScript)
    
    this.log('Demo data reset script created', 'success')
    return scriptPath
  }

  async createResetInstructions() {
    this.log('Creating reset instructions...')
    
    const instructions = `# Demo Data Reset Instructions

## 🔄 How to Reset Demo Data

### Method 1: Using phpMyAdmin (Recommended)
1. **Login to cPanel**
2. **Open phpMyAdmin**
3. **Select database:** \`${this.config.database.database}\`
4. **Click "Import" tab**
5. **Choose file:** \`demo-data-reset.sql\`
6. **Click "Go"**

### Method 2: Using MySQL Command Line
\`\`\`bash
mysql -h ${this.config.database.host} -u ${this.config.database.username} -p ${this.config.database.database} < demo-data-reset.sql
\`\`\`

### Method 3: Using npm Script
\`\`\`bash
npm run reset:demo
\`\`\`

## 📊 What Gets Reset

### Cleared Data:
- All demo customers and their data
- All demo shipments and tracking updates
- All demo quotes and quote items
- All demo invoices and invoice items
- All demo inquiries
- All demo user accounts

### Fresh Demo Data Created:
- **3 Demo Customers** with realistic profiles
- **3 Demo Shipments** in different statuses
- **3 Demo Quotes** with various services
- **2 Demo Invoices** (1 paid, 1 pending)
- **2 Demo Inquiries** for testing
- **1 Demo Admin User** with full access

## 🔑 Demo Credentials After Reset

### Admin Access:
**URL:** ${this.config.siteUrl}/admin
**Email:** <EMAIL>
**Password:** DemoAdmin2024!

### Customer Access:
**URL:** ${this.config.siteUrl}/customer
**Email:** <EMAIL>
**Password:** DemoCustomer2024!

## ⏰ Automatic Reset Schedule

Demo data automatically resets:
- **Daily at 2:00 AM UTC**
- **Before client presentations** (manual)
- **When demo reaches limits** (automatic)

## 🎯 When to Reset Demo Data

### Before Client Presentations:
- Clean slate for demonstrations
- Consistent demo scenarios
- Predictable data for walkthroughs

### After Heavy Testing:
- Remove test data clutter
- Reset to known good state
- Clear any corrupted demo data

### Regular Maintenance:
- Keep demo responsive
- Prevent data bloat
- Maintain demo quality

## 📋 Post-Reset Checklist

After resetting demo data:
- [ ] Verify admin login works
- [ ] Check customer portal access
- [ ] Test shipment tracking
- [ ] Confirm quote system
- [ ] Validate invoice generation
- [ ] Test contact forms
- [ ] Verify email notifications

## 🆘 Troubleshooting

### If Reset Fails:
1. Check database connection
2. Verify user permissions
3. Check for foreign key constraints
4. Review error logs in cPanel

### If Demo Doesn't Work After Reset:
1. Clear browser cache
2. Check .htaccess configuration
3. Verify environment variables
4. Test database connectivity

---

**Generated on:** ${new Date().toISOString()}
**Demo Database:** ${this.config.database.database}
**Demo URL:** ${this.config.siteUrl}
`

    const instructionsPath = path.join(this.projectRoot, 'DEMO_RESET_INSTRUCTIONS.md')
    fs.writeFileSync(instructionsPath, instructions)
    
    this.log('Reset instructions created', 'success')
    return instructionsPath
  }

  async reset() {
    try {
      this.log('🔄 Starting demo data reset process...')
      
      // Create reset script
      const scriptPath = await this.createResetScript()
      
      // Create instructions
      const instructionsPath = await this.createResetInstructions()
      
      this.log('🎉 Demo data reset files created successfully!', 'success')
      this.log(`Reset script: ${scriptPath}`)
      this.log(`Instructions: ${instructionsPath}`)
      this.log('')
      this.log('📋 Next Steps:')
      this.log('1. Run the SQL script in phpMyAdmin')
      this.log('2. Test the demo website with fresh data')
      this.log('3. Verify all demo credentials work')
      this.log('4. Share updated demo with clients')
      
    } catch (error) {
      this.log(`Demo reset failed: ${error.message}`, 'error')
      process.exit(1)
    }
  }
}

// Run reset if called directly
if (require.main === module) {
  const resetter = new DemoDataReset()
  resetter.reset()
}

module.exports = DemoDataReset
