{"name": "dcf-logistics-production", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "build:godaddy": "npm run clean && npm run build:static && npm run optimize", "build:static": "next build && next export", "start": "next start", "lint": "next lint", "clean": "rimraf .next && rimraf out && rimraf node_modules/.cache", "optimize": "npm run compress:images && npm run minify:css", "compress:images": "node scripts/compress-images.js", "minify:css": "node scripts/minify-css.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate deploy", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "deploy:godaddy": "node scripts/deploy-godaddy.js", "deploy:ftp": "node scripts/ftp-deploy.js", "test:deployment": "node scripts/test-deployment.js", "backup:db": "node scripts/backup-database.js", "restore:db": "node scripts/restore-database.js"}, "dependencies": {"@ai-sdk/google": "latest", "@ai-sdk/openai": "latest", "@auth/prisma-adapter": "^2.9.1", "@hookform/resolvers": "^3.9.1", "@prisma/client": "^6.8.2", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@react-email/components": "^0.0.41", "@sendgrid/mail": "^8.1.5", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.3.1", "@tanstack/react-query": "^5.77.2", "@trpc/client": "^11.1.4", "@trpc/next": "^11.1.4", "@trpc/react-query": "^11.1.4", "@trpc/server": "^11.1.4", "@types/bcryptjs": "^2.4.6", "@types/multer": "^1.4.12", "@types/nodemailer": "^6.4.17", "@types/ws": "^8.18.1", "ai": "latest", "autoprefixer": "^10.4.20", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "cloudinary": "^2.6.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^4.1.0", "date-fns-tz": "^3.2.0", "embla-carousel-react": "8.5.1", "input-otp": "1.4.1", "lru-cache": "^11.1.0", "lucide-react": "^0.454.0", "multer": "^2.0.0", "next": "15.2.4", "next-auth": "^4.24.11", "next-cloudinary": "^6.16.0", "next-themes": "^0.4.4", "nodemailer": "^6.10.1", "prisma": "^6.8.2", "react": "^19", "react-day-picker": "^9.0.0", "react-dom": "^19", "react-email": "^4.0.15", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "^2.15.0", "sharp": "^0.34.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "sonner": "^1.7.4", "stripe": "^18.2.0", "superjson": "^2.2.2", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "tsx": "^4.19.4", "vaul": "^1.0.0", "web-push": "latest", "ws": "^8.18.2", "zod": "^3.25.34"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "basic-ftp": "^5.0.5", "imagemin": "^8.0.1", "imagemin-mozjpeg": "^10.0.0", "imagemin-pngquant": "^9.0.2", "postcss": "^8", "rimraf": "^6.0.1", "tailwindcss": "^3.4.17", "typescript": "^5"}}