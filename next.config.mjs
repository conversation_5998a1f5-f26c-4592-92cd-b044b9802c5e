/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable static export for shared hosting
  output: 'export',
  trailingSlash: true,
  skipTrailingSlashRedirect: true,
  distDir: 'out',

  // Disable server-side features
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },

  // Optimize images for static export
  images: {
    unoptimized: true,
  },

  // Environment variables for static build
  env: {
    STATIC_EXPORT: 'true',
    NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL || 'https://dcflogistics.com',
    NEXT_PUBLIC_CONTACT_EMAIL: process.env.NEXT_PUBLIC_CONTACT_EMAIL || '<EMAIL>',
    NEXT_PUBLIC_PHONE: process.env.NEXT_PUBLIC_PHONE || '+************',
  },

  // Webpack configuration for static export
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        os: false,
        path: false,
        querystring: false,
        util: false,
        buffer: false,
        events: false,
      }
    }
    return config
  },
}

export default nextConfig
