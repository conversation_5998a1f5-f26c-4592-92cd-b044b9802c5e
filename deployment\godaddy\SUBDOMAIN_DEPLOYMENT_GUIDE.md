# DCF Logistics - GoDaddy Subdomain Deployment Guide

## 🎯 **Client Testing Subdomain Setup**

This guide will help you deploy DCF Logistics to a GoDaddy subdomain for client testing and demonstrations.

---

## 📋 **Step 1: Create Subdomain in GoDaddy cPanel**

### **1.1 Access cPanel**
1. **Login to your GoDaddy account**
2. **Go to "My Products"**
3. **Find your hosting plan** and click "Manage"
4. **Click "cPanel Admin"**

### **1.2 Create Subdomain**
1. **In cPanel, find "Subdomains"** (usually under "Domains" section)
2. **Click "Subdomains"**
3. **Create new subdomain:**
   - **Subdomain:** `demo` or `test` or `client-demo`
   - **Domain:** Select your main domain
   - **Document Root:** Will auto-populate (e.g., `/public_html/demo`)
4. **Click "Create"**

### **1.3 Verify Subdomain**
- **Your subdomain will be:** `demo.yourdomain.com`
- **Document root:** `/public_html/demo/`
- **Wait 5-10 minutes** for DNS propagation

---

## 🗄️ **Step 2: Database Setup for Testing**

### **2.1 Create Testing Database**
1. **In cPanel, go to "MySQL Databases"**
2. **Create new database:**
   - **Database Name:** `dcf_demo` or `dcf_test`
   - **Click "Create Database"**
3. **Note the full database name:** `yourusername_dcf_demo`

### **2.2 Create Database User**
1. **Create new user:**
   - **Username:** `demo_user`
   - **Password:** Generate strong password
   - **Click "Create User"**
2. **Note the full username:** `yourusername_demo_user`

### **2.3 Add User to Database**
1. **In "Add User to Database" section:**
   - **User:** Select `yourusername_demo_user`
   - **Database:** Select `yourusername_dcf_demo`
   - **Click "Add"**
2. **Grant ALL PRIVILEGES**
3. **Click "Make Changes"**

### **2.4 Note Database Credentials**
```
Host: localhost
Database: yourusername_dcf_demo
Username: yourusername_demo_user
Password: [your generated password]
Port: 3306
```

---

## ⚙️ **Step 3: Configure Environment for Testing**

### **3.1 Create Testing Environment File**

Create `.env.demo` in your project root:

```env
# Demo Environment Configuration
NODE_ENV=production
DEMO_MODE=true

# Subdomain Configuration
NEXTAUTH_URL=https://demo.yourdomain.com
APP_URL=https://demo.yourdomain.com

# Security
NEXTAUTH_SECRET=your-demo-secret-key-32-characters-long

# Database (Testing)
DATABASE_URL=mysql://yourusername_demo_user:password@localhost:3306/yourusername_dcf_demo

# Email Configuration (Testing)
SENDGRID_API_KEY=SG.your-sendgrid-api-key
SENDGRID_FROM_EMAIL=<EMAIL>
SENDGRID_FROM_NAME=DCF Logistics Demo

# Contact Information (Demo)
CONTACT_EMAIL=<EMAIL>
SALES_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>

# Demo Branding
SITE_NAME=DCF Logistics Demo
SITE_DESCRIPTION=Logistics Management System - Demo Version

# Demo Restrictions
DEMO_RESTRICTIONS=true
MAX_DEMO_USERS=10
MAX_DEMO_SHIPMENTS=50
DEMO_DATA_RESET=daily
```

---

## 🔧 **Step 4: Build and Deploy to Subdomain**

### **4.1 Update Deployment Configuration**

Create `deployment/godaddy/config.demo.json`:

```json
{
  "ftp": {
    "host": "ftp.yourdomain.com",
    "user": "your-ftp-username",
    "password": "your-ftp-password",
    "port": 21,
    "secure": false
  },
  "remoteDir": "/public_html/demo",
  "siteUrl": "https://demo.yourdomain.com",
  "environment": "demo",
  "database": {
    "host": "localhost",
    "port": 3306,
    "database": "yourusername_dcf_demo",
    "username": "yourusername_demo_user",
    "password": "your-database-password"
  }
}
```

### **4.2 Deploy to Subdomain**

```bash
# Build for demo deployment
npm run build:demo

# Deploy to subdomain
npm run deploy:demo
```

---

## 🎨 **Step 5: Demo Environment Customization**

### **5.1 Demo Banner Configuration**

The demo will automatically include:
- **Demo banner** at the top of all pages
- **"DEMO VERSION"** watermark
- **Limited functionality** warnings
- **Data reset** notifications

### **5.2 Demo Data Setup**

After deployment, the system will include:
- **Sample customers** (5 demo customers)
- **Sample shipments** (10 demo shipments)
- **Sample quotes** (8 demo quotes)
- **Sample invoices** (6 demo invoices)
- **Demo admin user** (see credentials below)

---

## 👥 **Step 6: Client Access Setup**

### **6.1 Demo Admin Credentials**

**Admin Login:** `https://demo.yourdomain.com/admin`

```
Email: <EMAIL>
Password: DemoAdmin2024!
```

**⚠️ Important:** Change these credentials after deployment!

### **6.2 Demo Customer Credentials**

**Customer Portal:** `https://demo.yourdomain.com/customer`

```
Email: <EMAIL>
Password: DemoCustomer2024!
```

### **6.3 Client Testing Instructions**

Create `CLIENT_TESTING_GUIDE.md` for your clients:

```markdown
# DCF Logistics Demo - Testing Guide

## Demo Website
**URL:** https://demo.yourdomain.com

## What You Can Test

### 1. Public Website
- Homepage and navigation
- Services pages
- Contact forms
- Quote request system

### 2. Admin Dashboard
**Login:** https://demo.yourdomain.com/admin
**Email:** <EMAIL>
**Password:** DemoAdmin2024!

**Features to Test:**
- Customer management
- Shipment tracking
- Invoice generation
- Quote management
- Reports and analytics

### 3. Customer Portal
**Login:** https://demo.yourdomain.com/customer
**Email:** <EMAIL>
**Password:** DemoCustomer2024!

**Features to Test:**
- Track shipments
- View invoices
- Request quotes
- Update profile

## Demo Limitations
- Data resets daily at 2 AM UTC
- Maximum 10 demo users
- Maximum 50 demo shipments
- Email notifications go to demo addresses
- Payment processing is in test mode

## Support
For questions about the demo, contact:
**Email:** <EMAIL>
**Phone:** +220-XXX-XXXX
```

---

## 🚀 **Step 7: Automated Deployment Commands**

### **7.1 Add Demo Scripts to package.json**

```json
{
  "scripts": {
    "build:demo": "node deployment/godaddy/scripts/build-demo.js",
    "deploy:demo": "node deployment/godaddy/scripts/deploy-demo.js",
    "reset:demo": "node deployment/godaddy/scripts/reset-demo-data.js"
  }
}
```

### **7.2 Quick Deployment Commands**

```bash
# Full demo deployment (one command)
npm run deploy:demo

# Just build demo version
npm run build:demo

# Reset demo data (for client testing)
npm run reset:demo
```

---

## 🔒 **Step 8: Security and Maintenance**

### **8.1 Demo Security Settings**

- **Rate limiting** enabled (stricter for demo)
- **File upload** restrictions
- **Database** size limits
- **Email** sending limits
- **API** request limits

### **8.2 Automated Maintenance**

**Daily Tasks (2 AM UTC):**
- Reset demo data to default state
- Clean up uploaded files
- Reset user accounts to demo state
- Clear temporary data
- Send daily demo usage report

### **8.3 Monitoring**

**Demo Health Checks:**
- Uptime monitoring
- Performance tracking
- Error rate monitoring
- Demo data integrity checks

---

## 📊 **Step 9: Client Presentation Setup**

### **9.1 Demo Scenarios**

**Scenario 1: New Shipment Process**
1. Customer requests quote
2. Admin creates quote
3. Quote approval and invoice generation
4. Shipment creation and tracking
5. Delivery confirmation

**Scenario 2: Customer Portal**
1. Customer login
2. Track existing shipments
3. View invoice history
4. Request new quote
5. Update profile information

**Scenario 3: Admin Management**
1. Dashboard overview
2. Customer management
3. Shipment operations
4. Financial reporting
5. System configuration

### **9.2 Demo Data Reset**

```bash
# Reset demo to clean state for client presentation
npm run reset:demo
```

---

## ✅ **Deployment Checklist**

### **Pre-Deployment**
- [ ] Subdomain created in cPanel
- [ ] Demo database created and configured
- [ ] Environment variables configured
- [ ] Demo configuration file created
- [ ] FTP credentials verified

### **Deployment**
- [ ] Application built for demo
- [ ] Files uploaded to subdomain directory
- [ ] Database migrations run
- [ ] Demo data seeded
- [ ] SSL certificate configured
- [ ] Demo banner and restrictions active

### **Post-Deployment**
- [ ] Demo website accessible
- [ ] Admin dashboard functional
- [ ] Customer portal working
- [ ] Contact forms sending emails
- [ ] Demo credentials working
- [ ] Client testing guide prepared

### **Client Handoff**
- [ ] Demo URL shared with client
- [ ] Testing credentials provided
- [ ] Client testing guide delivered
- [ ] Demo scenarios documented
- [ ] Support contact information shared

---

## 🆘 **Troubleshooting**

### **Common Issues**

**1. Subdomain not accessible**
- Check DNS propagation (can take up to 24 hours)
- Verify subdomain configuration in cPanel
- Check document root path

**2. Database connection failed**
- Verify database credentials
- Check database user privileges
- Ensure database exists

**3. Demo features not working**
- Verify DEMO_MODE=true in environment
- Check demo configuration file
- Review demo restrictions settings

**4. SSL certificate issues**
- Enable SSL for subdomain in cPanel
- Force HTTPS redirect in .htaccess
- Wait for certificate propagation

---

## 📞 **Support**

**For deployment issues:**
- **Email:** <EMAIL>
- **Phone:** +220-XXX-XXXX

**For client demo support:**
- **Email:** <EMAIL>
- **Demo URL:** https://demo.yourdomain.com

---

**Your DCF Logistics demo will be live at: `https://demo.yourdomain.com`**

**Estimated setup time: 45-60 minutes**
