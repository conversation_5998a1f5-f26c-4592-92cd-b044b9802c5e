#!/usr/bin/env node

/**
 * Demo Build Script for DCF Logistics
 * Builds the application specifically for client testing on subdomain
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

class DemoBuilder {
  constructor() {
    this.projectRoot = process.cwd()
    this.buildDir = path.join(this.projectRoot, 'out')
    this.deploymentDir = path.join(this.projectRoot, 'deployment', 'godaddy')
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString()
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️'
    console.log(`${prefix} [${timestamp}] ${message}`)
  }

  async cleanBuildDir() {
    this.log('Cleaning previous demo build...')
    if (fs.existsSync(this.buildDir)) {
      fs.rmSync(this.buildDir, { recursive: true, force: true })
    }
  }

  async prepareDemoConfiguration() {
    this.log('Preparing demo configuration...')
    
    // Create demo-specific Next.js config
    const demoConfig = `/** @type {import('next').NextConfig} */
const nextConfig = {
  // Enable static export for shared hosting
  output: 'export',
  trailingSlash: true,
  skipTrailingSlashRedirect: true,
  distDir: 'out',
  
  // Demo-specific settings
  images: {
    unoptimized: true,
    loader: 'custom',
    loaderFile: './lib/image-loader.js'
  },
  
  // Build optimizations
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  
  // Demo environment variables
  env: {
    DEMO_MODE: 'true',
    NEXTAUTH_URL: process.env.NEXTAUTH_URL,
    APP_URL: process.env.APP_URL,
    CONTACT_EMAIL: process.env.CONTACT_EMAIL,
    STATIC_EXPORT: 'true',
    DEMO_RESTRICTIONS: 'true',
  },
  
  // Demo redirects
  async redirects() {
    return [
      {
        source: '/admin',
        destination: '/admin/dashboard',
        permanent: true,
      },
      {
        source: '/demo',
        destination: '/',
        permanent: false,
      },
    ]
  },
  
  // Security headers for demo
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'SAMEORIGIN',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'X-Demo-Environment',
            value: 'true',
          },
        ],
      },
    ]
  },
  
  // Webpack configuration for demo
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
      }
    }
    return config
  },
}

export default nextConfig`

    const currentConfig = path.join(this.projectRoot, 'next.config.mjs')
    
    // Backup current config
    if (fs.existsSync(currentConfig)) {
      fs.copyFileSync(currentConfig, `${currentConfig}.backup`)
    }
    
    // Write demo config
    fs.writeFileSync(currentConfig, demoConfig)
    
    this.log('Demo configuration prepared', 'success')
  }

  async createDemoEnvironment() {
    this.log('Creating demo environment file...')
    
    const demoEnv = `# Demo Environment - Auto-generated
NODE_ENV=production
DEMO_MODE=true
DEMO_RESTRICTIONS=true
STATIC_EXPORT=true

# Demo branding
SITE_NAME=DCF Logistics Demo
SITE_DESCRIPTION=Logistics Management System - Demo Version

# Demo limitations
MAX_DEMO_USERS=10
MAX_DEMO_SHIPMENTS=50
DEMO_DATA_RESET=daily

# Demo credentials (change after deployment)
DEMO_ADMIN_EMAIL=<EMAIL>
DEMO_ADMIN_PASSWORD=DemoAdmin2024!
DEMO_CUSTOMER_EMAIL=<EMAIL>
DEMO_CUSTOMER_PASSWORD=DemoCustomer2024!
`

    fs.writeFileSync(path.join(this.projectRoot, '.env.demo'), demoEnv)
    this.log('Demo environment file created', 'success')
  }

  async buildApplication() {
    this.log('Building demo application...')
    
    try {
      // Install dependencies
      this.log('Installing dependencies...')
      execSync('npm ci', { stdio: 'inherit' })
      
      // Generate Prisma client
      this.log('Generating Prisma client...')
      execSync('npx prisma generate', { stdio: 'inherit' })
      
      // Build the application
      this.log('Building Next.js application...')
      execSync('npm run build', { stdio: 'inherit' })
      
      this.log('Demo application built successfully', 'success')
    } catch (error) {
      this.log(`Build failed: ${error.message}`, 'error')
      throw error
    }
  }

  async restoreConfiguration() {
    this.log('Restoring original configuration...')
    
    const currentConfig = path.join(this.projectRoot, 'next.config.mjs')
    const backupConfig = `${currentConfig}.backup`
    
    if (fs.existsSync(backupConfig)) {
      fs.copyFileSync(backupConfig, currentConfig)
      fs.unlinkSync(backupConfig)
      this.log('Original configuration restored', 'success')
    }
  }

  async createDemoAssets() {
    this.log('Creating demo-specific assets...')
    
    // Create demo banner CSS
    const demoBannerCSS = `
/* Demo Banner Styles */
.demo-banner {
  background: linear-gradient(45deg, #ff6b6b, #ffa500);
  color: white;
  text-align: center;
  padding: 10px;
  font-weight: bold;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 9999;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.demo-banner a {
  color: white;
  text-decoration: underline;
}

.demo-watermark {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: rgba(255, 107, 107, 0.9);
  color: white;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  z-index: 1000;
}

/* Adjust body for demo banner */
body.demo-mode {
  padding-top: 50px;
}
`

    fs.writeFileSync(path.join(this.buildDir, 'demo-styles.css'), demoBannerCSS)

    // Create demo JavaScript
    const demoJS = `
// Demo Mode JavaScript
(function() {
  // Add demo banner
  function addDemoBanner() {
    const banner = document.createElement('div');
    banner.className = 'demo-banner';
    banner.innerHTML = \`
      🚧 DEMO VERSION - For Testing Purposes Only | 
      <a href="mailto:<EMAIL>">Contact for Full Version</a>
    \`;
    document.body.insertBefore(banner, document.body.firstChild);
    document.body.classList.add('demo-mode');
  }

  // Add demo watermark
  function addDemoWatermark() {
    const watermark = document.createElement('div');
    watermark.className = 'demo-watermark';
    watermark.textContent = 'DEMO';
    document.body.appendChild(watermark);
  }

  // Initialize demo features
  function initDemo() {
    addDemoBanner();
    addDemoWatermark();
    
    // Add demo restrictions
    console.log('%c🚧 DEMO MODE ACTIVE', 'color: #ff6b6b; font-size: 16px; font-weight: bold;');
    console.log('This is a demonstration version with limited functionality.');
  }

  // Run when DOM is ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initDemo);
  } else {
    initDemo();
  }
})();
`

    fs.writeFileSync(path.join(this.buildDir, 'demo-script.js'), demoJS)

    this.log('Demo assets created', 'success')
  }

  async updateHtmlFiles() {
    this.log('Updating HTML files for demo mode...')
    
    const htmlFiles = this.findHtmlFiles(this.buildDir)
    
    for (const htmlFile of htmlFiles) {
      let content = fs.readFileSync(htmlFile, 'utf8')
      
      // Add demo styles and scripts
      const demoIncludes = `
  <link rel="stylesheet" href="/demo-styles.css">
  <script src="/demo-script.js"></script>
</head>`
      
      content = content.replace('</head>', demoIncludes)
      
      // Update title to include "Demo"
      content = content.replace(/<title>(.*?)<\/title>/, '<title>$1 - Demo</title>')
      
      fs.writeFileSync(htmlFile, content)
    }
    
    this.log(`Updated ${htmlFiles.length} HTML files for demo mode`, 'success')
  }

  findHtmlFiles(dir) {
    const htmlFiles = []
    const items = fs.readdirSync(dir)

    for (const item of items) {
      const fullPath = path.join(dir, item)
      const stat = fs.statSync(fullPath)

      if (stat.isDirectory()) {
        htmlFiles.push(...this.findHtmlFiles(fullPath))
      } else if (item.endsWith('.html')) {
        htmlFiles.push(fullPath)
      }
    }

    return htmlFiles
  }

  async createDemoHtaccess() {
    this.log('Creating demo .htaccess...')
    
    const htaccessContent = `# DCF Logistics Demo - .htaccess Configuration

# Enable Rewrite Engine
RewriteEngine On

# Force HTTPS (if SSL is available)
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Handle Next.js routing
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteCond %{REQUEST_URI} !^/api/
RewriteRule ^(.*)$ /index.html [L]

# Demo-specific headers
<IfModule mod_headers.c>
    Header always set X-Demo-Environment "true"
    Header always set X-Frame-Options SAMEORIGIN
    Header always set X-Content-Type-Options nosniff
    Header always set X-Demo-Notice "This is a demonstration version"
</IfModule>

# Demo restrictions
<Files "*.env*">
    Order allow,deny
    Deny from all
</Files>

# Compression for demo
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/html text/css application/javascript
</IfModule>

# Cache demo assets
<IfModule mod_expires.c>
    ExpiresActive on
    ExpiresByType text/css "access plus 1 day"
    ExpiresByType application/javascript "access plus 1 day"
    ExpiresByType image/* "access plus 1 day"
</IfModule>`

    fs.writeFileSync(path.join(this.buildDir, '.htaccess'), htaccessContent)
    this.log('Demo .htaccess created', 'success')
  }

  async build() {
    try {
      this.log('🚧 Starting demo build process...')
      
      await this.cleanBuildDir()
      await this.prepareDemoConfiguration()
      await this.createDemoEnvironment()
      await this.buildApplication()
      await this.restoreConfiguration()
      await this.createDemoAssets()
      await this.updateHtmlFiles()
      await this.createDemoHtaccess()
      
      this.log('🎉 Demo build completed successfully!', 'success')
      this.log(`Demo files ready in: ${this.buildDir}`)
      this.log('Next step: Run "npm run deploy:demo" to deploy to subdomain')
      
    } catch (error) {
      this.log(`Demo build failed: ${error.message}`, 'error')
      
      // Restore configuration on error
      try {
        await this.restoreConfiguration()
      } catch (restoreError) {
        this.log(`Failed to restore configuration: ${restoreError.message}`, 'error')
      }
      
      process.exit(1)
    }
  }
}

// Run build if called directly
if (require.main === module) {
  const builder = new DemoBuilder()
  builder.build()
}

module.exports = DemoBuilder
