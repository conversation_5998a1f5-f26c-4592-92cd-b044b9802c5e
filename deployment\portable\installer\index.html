<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DCF Logistics - Installation Wizard</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #007cba 0%, #005a8b 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .installer-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            width: 100%;
            overflow: hidden;
        }
        
        .header {
            background: #007cba;
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .header p {
            opacity: 0.9;
            font-size: 16px;
        }
        
        .content {
            padding: 40px;
        }
        
        .step {
            display: none;
        }
        
        .step.active {
            display: block;
        }
        
        .step h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 24px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }
        
        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px;
            border: 2px solid #e1e5e9;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #007cba;
        }
        
        .form-group small {
            color: #666;
            font-size: 14px;
            margin-top: 5px;
            display: block;
        }
        
        .button-group {
            display: flex;
            justify-content: space-between;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e1e5e9;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .btn-primary {
            background: #007cba;
            color: white;
        }
        
        .btn-primary:hover {
            background: #005a8b;
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #6c757d;
            border: 1px solid #dee2e6;
        }
        
        .btn-secondary:hover {
            background: #e9ecef;
        }
        
        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        
        .progress-bar {
            height: 4px;
            background: #e1e5e9;
            margin-bottom: 30px;
            border-radius: 2px;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: #007cba;
            transition: width 0.3s;
            border-radius: 2px;
        }
        
        .alert {
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .loading {
            display: none;
            text-align: center;
            padding: 40px;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #007cba;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .feature-list {
            list-style: none;
            margin: 20px 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            position: relative;
            padding-left: 25px;
        }
        
        .feature-list li:before {
            content: "✓";
            position: absolute;
            left: 0;
            color: #28a745;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="installer-container">
        <div class="header">
            <h1>DCF Logistics</h1>
            <p>Installation Wizard</p>
        </div>
        
        <div class="content">
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 20%"></div>
            </div>
            
            <!-- Step 1: Welcome -->
            <div class="step active" id="step1">
                <h2>Welcome to DCF Logistics</h2>
                <p>This wizard will guide you through the installation process. The setup should take about 5 minutes.</p>
                
                <ul class="feature-list">
                    <li>Complete logistics management system</li>
                    <li>Customer portal and admin dashboard</li>
                    <li>Shipment tracking and invoicing</li>
                    <li>Email notifications and reporting</li>
                    <li>Mobile-responsive design</li>
                </ul>
                
                <p><strong>System Requirements:</strong></p>
                <ul class="feature-list">
                    <li>Node.js 18 or higher</li>
                    <li>500MB free disk space</li>
                    <li>Modern web browser</li>
                </ul>
            </div>
            
            <!-- Step 2: Database Configuration -->
            <div class="step" id="step2">
                <h2>Database Configuration</h2>
                
                <div class="form-group">
                    <label for="databaseType">Database Type</label>
                    <select id="databaseType" onchange="toggleDatabaseFields()">
                        <option value="sqlite">SQLite (Recommended for small deployments)</option>
                        <option value="mysql">MySQL</option>
                        <option value="postgresql">PostgreSQL</option>
                    </select>
                    <small>SQLite is recommended for most installations</small>
                </div>
                
                <div id="externalDbFields" style="display: none;">
                    <div class="form-group">
                        <label for="dbHost">Database Host</label>
                        <input type="text" id="dbHost" placeholder="localhost">
                    </div>
                    
                    <div class="form-group">
                        <label for="dbPort">Database Port</label>
                        <input type="number" id="dbPort" placeholder="3306">
                    </div>
                    
                    <div class="form-group">
                        <label for="dbName">Database Name</label>
                        <input type="text" id="dbName" placeholder="dcf_logistics">
                    </div>
                    
                    <div class="form-group">
                        <label for="dbUser">Database Username</label>
                        <input type="text" id="dbUser" placeholder="username">
                    </div>
                    
                    <div class="form-group">
                        <label for="dbPassword">Database Password</label>
                        <input type="password" id="dbPassword" placeholder="password">
                    </div>
                </div>
            </div>
            
            <!-- Step 3: Site Configuration -->
            <div class="step" id="step3">
                <h2>Site Configuration</h2>
                
                <div class="form-group">
                    <label for="siteUrl">Site URL</label>
                    <input type="url" id="siteUrl" placeholder="https://yourdomain.com" required>
                    <small>The full URL where your site will be accessible</small>
                </div>
                
                <div class="form-group">
                    <label for="siteName">Company Name</label>
                    <input type="text" id="siteName" placeholder="DCF Logistics" value="DCF Logistics">
                </div>
                
                <div class="form-group">
                    <label for="adminEmail">Admin Email</label>
                    <input type="email" id="adminEmail" placeholder="<EMAIL>" required>
                    <small>This will be used for system notifications</small>
                </div>
            </div>
            
            <!-- Step 4: Admin User -->
            <div class="step" id="step4">
                <h2>Create Admin User</h2>
                
                <div class="form-group">
                    <label for="adminName">Full Name</label>
                    <input type="text" id="adminName" placeholder="Administrator" required>
                </div>
                
                <div class="form-group">
                    <label for="adminUsername">Admin Email</label>
                    <input type="email" id="adminUsername" placeholder="<EMAIL>" required>
                </div>
                
                <div class="form-group">
                    <label for="adminPassword">Password</label>
                    <input type="password" id="adminPassword" placeholder="Choose a strong password" required>
                    <small>Minimum 8 characters with letters and numbers</small>
                </div>
                
                <div class="form-group">
                    <label for="adminPasswordConfirm">Confirm Password</label>
                    <input type="password" id="adminPasswordConfirm" placeholder="Confirm your password" required>
                </div>
            </div>
            
            <!-- Step 5: Email Configuration -->
            <div class="step" id="step5">
                <h2>Email Configuration (Optional)</h2>
                
                <div class="form-group">
                    <label for="emailProvider">Email Provider</label>
                    <select id="emailProvider">
                        <option value="">Skip email setup</option>
                        <option value="sendgrid">SendGrid</option>
                        <option value="smtp">SMTP Server</option>
                    </select>
                    <small>Email is required for notifications and password resets</small>
                </div>
                
                <div id="sendgridFields" style="display: none;">
                    <div class="form-group">
                        <label for="sendgridKey">SendGrid API Key</label>
                        <input type="password" id="sendgridKey" placeholder="SG.your-api-key">
                    </div>
                </div>
                
                <div id="smtpFields" style="display: none;">
                    <div class="form-group">
                        <label for="smtpHost">SMTP Host</label>
                        <input type="text" id="smtpHost" placeholder="smtp.gmail.com">
                    </div>
                    
                    <div class="form-group">
                        <label for="smtpPort">SMTP Port</label>
                        <input type="number" id="smtpPort" placeholder="587">
                    </div>
                    
                    <div class="form-group">
                        <label for="smtpUser">SMTP Username</label>
                        <input type="text" id="smtpUser" placeholder="<EMAIL>">
                    </div>
                    
                    <div class="form-group">
                        <label for="smtpPassword">SMTP Password</label>
                        <input type="password" id="smtpPassword" placeholder="your-password">
                    </div>
                </div>
            </div>
            
            <!-- Loading Step -->
            <div class="loading" id="loadingStep">
                <div class="spinner"></div>
                <h2>Installing DCF Logistics...</h2>
                <p id="loadingMessage">Setting up your application...</p>
            </div>
            
            <!-- Success Step -->
            <div class="step" id="successStep">
                <h2>Installation Complete!</h2>
                <div class="alert alert-success">
                    <strong>Success!</strong> DCF Logistics has been installed successfully.
                </div>
                
                <p>Your logistics management system is now ready to use.</p>
                
                <ul class="feature-list">
                    <li>Admin dashboard is accessible</li>
                    <li>Database has been configured</li>
                    <li>Admin user has been created</li>
                    <li>Email notifications are configured</li>
                </ul>
                
                <div class="button-group">
                    <button class="btn btn-primary" onclick="window.location.href='/admin'">
                        Go to Admin Dashboard
                    </button>
                </div>
            </div>
            
            <div class="button-group" id="navigationButtons">
                <button class="btn btn-secondary" id="prevBtn" onclick="previousStep()" style="display: none;">
                    Previous
                </button>
                <button class="btn btn-primary" id="nextBtn" onclick="nextStep()">
                    Next
                </button>
            </div>
        </div>
    </div>

    <script src="installer.js"></script>
</body>
</html>
