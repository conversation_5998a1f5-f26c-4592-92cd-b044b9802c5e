#!/usr/bin/env node

/**
 * Demo Deployment Script for DCF Logistics
 * Deploys the application to GoDaddy subdomain for client testing
 */

const fs = require('fs')
const path = require('path')
const ftp = require('basic-ftp')
const DemoBuilder = require('./build-demo')

class DemoDeployer {
  constructor() {
    this.projectRoot = process.cwd()
    this.buildDir = path.join(this.projectRoot, 'out')
    this.deploymentDir = path.join(this.projectRoot, 'deployment', 'godaddy')
    this.config = this.loadDemoConfig()
  }

  loadDemoConfig() {
    const configPath = path.join(this.deploymentDir, 'config.demo.json')
    if (!fs.existsSync(configPath)) {
      throw new Error(`Demo config not found: ${configPath}. Please create it first.`)
    }
    return JSON.parse(fs.readFileSync(configPath, 'utf8'))
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString()
    const prefix = type === 'error' ? '❌' : type === 'success' ? '✅' : 'ℹ️'
    console.log(`${prefix} [${timestamp}] ${message}`)
  }

  async createDemoBackup() {
    this.log('Creating backup of current demo deployment...')
    
    const backupDir = path.join(this.projectRoot, 'backups', 'demo')
    if (!fs.existsSync(backupDir)) {
      fs.mkdirSync(backupDir, { recursive: true })
    }

    const backupName = `demo-backup-${Date.now()}`
    const backupPath = path.join(backupDir, backupName)
    
    try {
      const client = new ftp.Client()
      await client.access(this.config.ftp)
      
      // Check if demo directory exists
      try {
        await client.list(this.config.remoteDir)
        
        // Download current demo for backup
        if (!fs.existsSync(backupPath)) {
          fs.mkdirSync(backupPath, { recursive: true })
        }
        
        await client.downloadToDir(backupPath, this.config.remoteDir)
        this.log(`Demo backup created: ${backupName}`, 'success')
      } catch (error) {
        this.log('No existing demo deployment found (first deployment)', 'info')
      }
      
      await client.close()
      return backupPath
    } catch (error) {
      this.log(`Backup failed: ${error.message}`, 'error')
      // Continue with deployment even if backup fails
      return null
    }
  }

  async buildDemo() {
    this.log('Building demo application...')
    
    const builder = new DemoBuilder()
    await builder.build()
    
    this.log('Demo application built successfully', 'success')
  }

  async uploadDemoFiles() {
    this.log('Uploading demo files to subdomain...')
    
    try {
      const client = new ftp.Client()
      client.ftp.verbose = false
      
      await client.access(this.config.ftp)
      
      // Ensure demo directory exists
      try {
        await client.ensureDir(this.config.remoteDir)
      } catch (error) {
        this.log(`Creating demo directory: ${this.config.remoteDir}`)
        await client.ensureDir(this.config.remoteDir)
      }
      
      // Clear existing demo files (except important ones)
      const preserveFiles = ['.htaccess', 'robots.txt', 'demo-data.json']
      try {
        const remoteFiles = await client.list(this.config.remoteDir)
        
        for (const file of remoteFiles) {
          if (!preserveFiles.includes(file.name) && file.name !== '.' && file.name !== '..') {
            try {
              if (file.isDirectory) {
                await client.removeDir(`${this.config.remoteDir}/${file.name}`)
              } else {
                await client.remove(`${this.config.remoteDir}/${file.name}`)
              }
            } catch (error) {
              this.log(`Warning: Could not remove ${file.name}: ${error.message}`)
            }
          }
        }
      } catch (error) {
        this.log('Demo directory is empty or does not exist')
      }
      
      // Upload new demo files
      this.log('Uploading demo files...')
      await client.uploadFromDir(this.buildDir, this.config.remoteDir)
      
      await client.close()
      this.log('Demo files uploaded successfully', 'success')
      
    } catch (error) {
      this.log(`Upload failed: ${error.message}`, 'error')
      throw error
    }
  }

  async createDemoDatabase() {
    this.log('Setting up demo database...')
    
    // Note: Database creation must be done manually in cPanel
    // This function will create the demo data structure
    
    const demoDataScript = `
-- Demo Database Setup Script
-- Run this in phpMyAdmin or MySQL command line

USE ${this.config.database.database};

-- Create demo admin user
INSERT IGNORE INTO users (id, email, name, role, password, emailVerified, createdAt, updatedAt) VALUES
('demo-admin-001', '<EMAIL>', 'Demo Administrator', 'ADMIN', '$2a$12$demo.hash.here', NOW(), NOW(), NOW());

-- Create demo customer
INSERT IGNORE INTO customers (id, name, email, phone, company, address, city, country, createdAt, updatedAt) VALUES
('demo-customer-001', 'Demo Customer', '<EMAIL>', '+************', 'Demo Company Ltd', '123 Demo Street', 'Banjul', 'Gambia', NOW(), NOW());

-- Create demo shipments
INSERT IGNORE INTO shipments (id, trackingNumber, customerId, origin, destination, status, weight, dimensions, createdAt, updatedAt) VALUES
('demo-shipment-001', 'DCF-DEMO-001', 'demo-customer-001', 'Banjul, Gambia', 'London, UK', 'IN_TRANSIT', 25.5, '50x40x30 cm', NOW(), NOW()),
('demo-shipment-002', 'DCF-DEMO-002', 'demo-customer-001', 'London, UK', 'New York, USA', 'DELIVERED', 15.2, '30x25x20 cm', DATE_SUB(NOW(), INTERVAL 5 DAY), NOW());

-- Create demo quotes
INSERT IGNORE INTO quotes (id, customerId, serviceType, origin, destination, weight, status, quotedAmount, validUntil, createdAt, updatedAt) VALUES
('demo-quote-001', 'demo-customer-001', 'AIR_FREIGHT', 'Banjul, Gambia', 'Paris, France', 45.0, 'PENDING', 850.00, DATE_ADD(NOW(), INTERVAL 30 DAY), NOW(), NOW());

-- Create demo invoices
INSERT IGNORE INTO invoices (id, customerId, invoiceNumber, totalAmount, status, dueDate, createdAt, updatedAt) VALUES
('demo-invoice-001', 'demo-customer-001', 'INV-DEMO-001', 1250.00, 'PAID', DATE_ADD(NOW(), INTERVAL 30 DAY), NOW(), NOW());
`

    const scriptPath = path.join(this.buildDir, 'demo-database-setup.sql')
    fs.writeFileSync(scriptPath, demoDataScript)
    
    this.log('Demo database script created', 'success')
    this.log(`Database script saved to: ${scriptPath}`)
    this.log('Please run this script in phpMyAdmin to set up demo data')
  }

  async testDemoDeployment() {
    this.log('Testing demo deployment...')
    
    try {
      const https = require('https')
      const http = require('http')
      
      const testUrl = this.config.siteUrl
      const client = testUrl.startsWith('https') ? https : http
      
      return new Promise((resolve, reject) => {
        const req = client.get(testUrl, (res) => {
          if (res.statusCode === 200) {
            this.log('Demo deployment test successful', 'success')
            resolve(true)
          } else {
            this.log(`Demo test failed: HTTP ${res.statusCode}`, 'error')
            reject(new Error(`HTTP ${res.statusCode}`))
          }
        })
        
        req.on('error', (error) => {
          this.log(`Demo test failed: ${error.message}`, 'error')
          reject(error)
        })
        
        req.setTimeout(15000, () => {
          this.log('Demo test timed out', 'error')
          reject(new Error('Timeout'))
        })
      })
    } catch (error) {
      this.log(`Demo test failed: ${error.message}`, 'error')
      throw error
    }
  }

  async createClientGuide() {
    this.log('Creating client testing guide...')
    
    const clientGuide = `# DCF Logistics Demo - Client Testing Guide

## 🌐 Demo Website
**URL:** ${this.config.siteUrl}

## 🔑 Demo Credentials

### Admin Dashboard
**URL:** ${this.config.siteUrl}/admin
**Email:** <EMAIL>
**Password:** DemoAdmin2024!

### Customer Portal
**URL:** ${this.config.siteUrl}/customer
**Email:** <EMAIL>
**Password:** DemoCustomer2024!

## 🧪 What to Test

### 1. Public Website Features
- ✅ Homepage and navigation
- ✅ Services information
- ✅ Contact forms
- ✅ Quote request system
- ✅ Company information

### 2. Admin Dashboard Features
- ✅ Customer management
- ✅ Shipment tracking and management
- ✅ Quote generation and management
- ✅ Invoice creation and tracking
- ✅ Reports and analytics
- ✅ User management
- ✅ System settings

### 3. Customer Portal Features
- ✅ Shipment tracking
- ✅ Invoice viewing and download
- ✅ Quote requests
- ✅ Profile management
- ✅ Communication history

## ⚠️ Demo Limitations

- **Data Reset:** Demo data resets daily at 2 AM UTC
- **User Limit:** Maximum 10 demo users
- **Shipment Limit:** Maximum 50 demo shipments
- **Email Notifications:** Sent to demo addresses only
- **Payment Processing:** Test mode only
- **File Uploads:** Limited to 5MB per file

## 📧 Demo Support

For questions about the demo or to discuss the full implementation:

**Email:** <EMAIL>
**Phone:** +220-XXX-XXXX
**Website:** ${this.config.siteUrl}

## 🚀 Next Steps

After testing the demo, we can discuss:
1. Customization requirements
2. Additional features needed
3. Integration with existing systems
4. Training and support plans
5. Deployment timeline

---

**Generated on:** ${new Date().toISOString()}
**Demo Version:** DCF Logistics v1.0 Demo
`

    const guidePath = path.join(this.buildDir, 'CLIENT_TESTING_GUIDE.md')
    fs.writeFileSync(guidePath, clientGuide)
    
    this.log('Client testing guide created', 'success')
    return guidePath
  }

  async deploy() {
    try {
      this.log('🚧 Starting demo deployment to subdomain...')
      
      // Create backup
      await this.createDemoBackup()
      
      // Build demo application
      await this.buildDemo()
      
      // Upload files
      await this.uploadDemoFiles()
      
      // Setup demo database
      await this.createDemoDatabase()
      
      // Test deployment
      await this.testDemoDeployment()
      
      // Create client guide
      const guidePath = await this.createClientGuide()
      
      this.log('🎉 Demo deployment completed successfully!', 'success')
      this.log(`Demo URL: ${this.config.siteUrl}`)
      this.log(`Client guide: ${guidePath}`)
      this.log('')
      this.log('📋 Next Steps:')
      this.log('1. Run the database setup script in phpMyAdmin')
      this.log('2. Test the demo website and admin dashboard')
      this.log('3. Share the client testing guide with your clients')
      this.log('4. Monitor demo usage and client feedback')
      
    } catch (error) {
      this.log(`Demo deployment failed: ${error.message}`, 'error')
      process.exit(1)
    }
  }
}

// Run deployment if called directly
if (require.main === module) {
  const deployer = new DemoDeployer()
  deployer.deploy()
}

module.exports = DemoDeployer
