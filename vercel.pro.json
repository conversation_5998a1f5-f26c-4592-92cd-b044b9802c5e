{"version": 2, "name": "dcf-logistics", "alias": ["dcf-logistics"], "regions": ["iad1"], "framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm ci", "devCommand": "npm run dev", "env": {"NEXTAUTH_URL": "@nextauth_url", "NEXTAUTH_SECRET": "@nextauth_secret", "DATABASE_URL": "@database_url", "SENDGRID_API_KEY": "@sendgrid_api_key", "SENDGRID_FROM_EMAIL": "@sendgrid_from_email", "SENDGRID_FROM_NAME": "@sendgrid_from_name", "CONTACT_EMAIL": "@contact_email", "SALES_EMAIL": "@sales_email", "ADMIN_EMAIL": "@admin_email", "APP_URL": "@app_url", "STRIPE_SECRET_KEY": "@stripe_secret_key", "STRIPE_PUBLISHABLE_KEY": "@stripe_publishable_key", "STRIPE_WEBHOOK_SECRET": "@stripe_webhook_secret", "CLOUDINARY_CLOUD_NAME": "@cloudinary_cloud_name", "CLOUDINARY_API_KEY": "@cloudinary_api_key", "CLOUDINARY_API_SECRET": "@cloudinary_api_secret", "TWILIO_ACCOUNT_SID": "@twilio_account_sid", "TWILIO_AUTH_TOKEN": "@twilio_auth_token", "TWILIO_PHONE_NUMBER": "@twilio_phone_number"}, "build": {"env": {"NEXTAUTH_URL": "@nextauth_url", "NEXTAUTH_SECRET": "@nextauth_secret", "DATABASE_URL": "@database_url"}}, "functions": {"app/api/**/*.ts": {"maxDuration": 30}, "app/api/**/*.js": {"maxDuration": 30}}, "headers": [{"source": "/(.*)", "headers": [{"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "Referrer-Policy", "value": "origin-when-cross-origin"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}]}, {"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}], "redirects": [{"source": "/admin", "destination": "/admin/dashboard", "permanent": true}, {"source": "/dashboard", "destination": "/admin/dashboard", "permanent": true}], "rewrites": [{"source": "/sitemap.xml", "destination": "/api/sitemap"}, {"source": "/robots.txt", "destination": "/api/robots"}], "crons": [{"path": "/api/cron/email-queue", "schedule": "*/5 * * * *"}, {"path": "/api/cron/cleanup", "schedule": "0 2 * * *"}, {"path": "/api/cron/daily-reports", "schedule": "0 8 * * *"}, {"path": "/api/cron/backup", "schedule": "0 3 * * 0"}]}