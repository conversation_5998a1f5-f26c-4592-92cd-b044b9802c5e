# DCF Logistics Demo - Quick Setup Guide

## 🚀 **5-Step Demo Deployment**

### **Step 1: Create Subdomain (5 minutes)**
1. **Login to GoDaddy cPanel**
2. **Go to "Subdomains"**
3. **Create subdomain:**
   - **Name:** `demo` or `test`
   - **Domain:** Your main domain
   - **Document Root:** `/public_html/demo`
4. **Click "Create"**

### **Step 2: Setup Demo Database (5 minutes)**
1. **In cPanel, go to "MySQL Databases"**
2. **Create database:** `dcf_demo`
3. **Create user:** `demo_user` with strong password
4. **Add user to database** with ALL PRIVILEGES
5. **Note credentials:**
   ```
   Database: yourusername_dcf_demo
   Username: yourusername_demo_user
   Password: [your password]
   ```

### **Step 3: Configure Demo Settings (3 minutes)**
1. **Copy demo config:**
   ```bash
   cp deployment/godaddy/config.demo.example.json deployment/godaddy/config.demo.json
   ```

2. **Edit `config.demo.json` with your details:**
   ```json
   {
     "ftp": {
       "host": "ftp.yourdomain.com",
       "user": "your-ftp-username",
       "password": "your-ftp-password"
     },
     "remoteDir": "/public_html/demo",
     "siteUrl": "https://demo.yourdomain.com",
     "database": {
       "database": "yourusername_dcf_demo",
       "username": "yourusername_demo_user",
       "password": "your-database-password"
     }
   }
   ```

### **Step 4: Deploy Demo (2 minutes)**
```bash
# One command deployment
npm run deploy:demo
```

This will:
- ✅ Build demo version with restrictions
- ✅ Upload files to subdomain
- ✅ Create demo database script
- ✅ Generate client testing guide

### **Step 5: Setup Demo Data (3 minutes)**
1. **Run the generated SQL script in phpMyAdmin:**
   - Open `demo-database-setup.sql`
   - Copy contents to phpMyAdmin
   - Execute the script

2. **Test demo access:**
   - **Website:** `https://demo.yourdomain.com`
   - **Admin:** `https://demo.yourdomain.com/admin`
   - **Credentials:** `<EMAIL>` / `DemoAdmin2024!`

---

## 🎯 **Demo Features**

### **What Clients Will See:**
- ✅ **Demo banner** on all pages
- ✅ **Complete logistics website**
- ✅ **Functional admin dashboard**
- ✅ **Customer portal**
- ✅ **Sample data** (customers, shipments, quotes)
- ✅ **Working contact forms**
- ✅ **Quote request system**
- ✅ **Invoice management**
- ✅ **Package tracking**

### **Demo Restrictions:**
- 🔒 **Data resets daily** at 2 AM UTC
- 🔒 **Maximum 10 demo users**
- 🔒 **Maximum 50 demo shipments**
- 🔒 **Email notifications** to demo addresses only
- 🔒 **File uploads** limited to 5MB
- 🔒 **Payment processing** in test mode

---

## 📋 **Client Access Information**

### **Demo Credentials:**
```
Website: https://demo.yourdomain.com

Admin Dashboard:
Email: <EMAIL>
Password: DemoAdmin2024!

Customer Portal:
Email: <EMAIL>
Password: DemoCustomer2024!
```

### **What Clients Can Test:**
1. **Public Website Features**
2. **Admin Dashboard Management**
3. **Customer Portal Experience**
4. **Quote Request Process**
5. **Shipment Tracking**
6. **Invoice Management**
7. **Contact Forms**
8. **Mobile Responsiveness**

---

## 🔄 **Demo Maintenance**

### **Reset Demo Data:**
```bash
# Reset to clean state for presentations
npm run reset:demo
```

### **Update Demo:**
```bash
# Redeploy with latest changes
npm run deploy:demo
```

### **Monitor Demo:**
- Check demo website daily
- Monitor client feedback
- Reset data before presentations
- Update credentials if needed

---

## 📞 **Client Support**

**Demo Support Email:** <EMAIL>
**Demo Website:** https://demo.yourdomain.com
**Support Phone:** +220-XXX-XXXX

---

## ✅ **Quick Commands Reference**

```bash
# Deploy demo to subdomain
npm run deploy:demo

# Reset demo data
npm run reset:demo

# Build demo only (no deploy)
npm run build:demo
```

---

## 🎉 **Success!**

Your DCF Logistics demo is now live at:
**https://demo.yourdomain.com**

**Total Setup Time:** ~18 minutes
**Client Ready:** ✅ Yes
**Demo Features:** ✅ All functional
**Support Ready:** ✅ Yes

**Share the demo URL and credentials with your clients for testing!**
